import { ServiceCardDeck } from '@/src/app/_components/Pages/Home/ServiceCardDeck';
import { Service } from '@/src/app/_interfaces/service-type';
import { render } from '@testing-library/react';

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} data-testid="service-image" {...props} />
  ),
}));

const mockServices: Service[] = [
  {
    id: 1,
    slug: 'service-1',
    name: 'Service 1',
    description: 'Description 1',
    imageUrl: '/images/service1.jpg',
    status: 'ATIVO',
    provider: {
      id: 1,
      name: 'Provider 1',
      imageUrl: '/images/provider1.jpg',
      providerUrl: 'https://provider1.com',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 10,
      finalPrice: 90,
    },
    availableIn: ['SP'],
    details: ['Detail 1'],
    serviceLimits: 'Limit 1',
    keywords: ['keyword1'],
    termsConditionsUrl: 'https://terms.com',
    preparations: 'Preparation 1',
  },
  {
    id: 2,
    slug: 'service-2',
    name: 'Service 2',
    description: 'Description 2',
    imageUrl: '/images/service2.jpg',
    status: 'ATIVO',
    provider: {
      id: 2,
      name: 'Provider 2',
      imageUrl: '/images/provider2.jpg',
      providerUrl: 'https://provider2.com',
      description: 'Provider description 2',
    },
    price: {
      priceId: 2,
      originalPrice: 200,
      discountPrice: 20,
      finalPrice: 180,
    },
    availableIn: ['RJ'],
    details: ['Detail 2'],
    serviceLimits: 'Limit 2',
    keywords: ['keyword2'],
    termsConditionsUrl: 'https://terms2.com',
    preparations: 'Preparation 2',
  },
  {
    id: 3,
    slug: 'service-3',
    name: 'Service 3',
    description: 'Description 3',
    imageUrl: '/images/service3.jpg',
    status: 'ATIVO',
    provider: {
      id: 3,
      name: 'Provider 3',
      imageUrl: '/images/provider3.jpg',
      providerUrl: 'https://provider3.com',
      description: 'Provider description 3',
    },
    price: {
      priceId: 3,
      originalPrice: 300,
      discountPrice: 30,
      finalPrice: 270,
    },
    availableIn: ['MG'],
    details: ['Detail 3'],
    serviceLimits: 'Limit 3',
    keywords: ['keyword3'],
    termsConditionsUrl: 'https://terms3.com',
    preparations: 'Preparation 3',
  },
];

describe('ServiceCardDeck', () => {
  it('renders decorative cards when no services provided', () => {
    const { container } = render(<ServiceCardDeck />);
    // Should render decorative cards even without services
    expect(container.firstChild).not.toBeNull();
    // Should have 3 decorative cards (no service images)
    const cards = container.querySelectorAll('div[style*="background"]');
    expect(cards.length).toBeGreaterThan(0);
  });

  it('renders service images when services are provided', () => {
    const { container } = render(<ServiceCardDeck services={mockServices} />);
    expect(container.firstChild).not.toBeNull();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ServiceCardDeck services={mockServices} className="custom-class" />
    );
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('filters out services without images', () => {
    const servicesWithoutImages = [
      ...mockServices,
      {
        ...mockServices[0],
        id: 4,
        imageUrl: '', // Empty image URL
        name: 'Service without image',
      },
    ];

    const { container } = render(<ServiceCardDeck services={servicesWithoutImages} />);
    // Should still render (since we have valid services)
    expect(container.firstChild).not.toBeNull();
  });
});
