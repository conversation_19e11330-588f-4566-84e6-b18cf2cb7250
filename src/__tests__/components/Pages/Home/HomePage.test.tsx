import { axiosInstance } from '@/src/app/_utils';
import { render } from '@testing-library/react';

// Import the HomePage component directly
const HomePage = require('@/src/app/(pages)/page').default;

// Mock the components that are used in HomePage
jest.mock('@/src/app/_components', () => ({
  DynamicFAQ: () => <div data-testid="dynamic-faq">FAQ Component</div>,
  ServiceNavigationMenuDesktop: () => <div data-testid="service-nav-menu">Navigation Menu</div>,
  AskForService: () => <div data-testid="ask-for-service">Ask For Service Component</div>,
  NewHero: () => <div data-testid="new-hero-component">New Hero Component</div>,
  ProviderInfoFlex: () => <div data-testid="provider-info-flex">Provider Info Flex</div>,
  ErrorDisplay: ({ message }: { message: string; fullPage?: boolean }) => (
    <div data-testid="error-display">{message}</div>
  ),
  Advantages: () => <div data-testid="advantages">Advantages Component</div>,
  HowItWorks: () => <div data-testid="how-it-works">How It Works Component</div>,
  DelayedSurveyMonkeyScript: () => <div data-testid="survey-monkey">Survey Monkey Script</div>,
  DynamicDelayedSurveyMonkey: () => (
    <div data-testid="dynamic-survey-monkey">Dynamic Survey Monkey</div>
  ),
}));

// Mock the axios instance and other utils
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
  convertToCarouselCategories: jest.fn().mockImplementation((categories) => categories),
}));

// Mock the Separator component
jest.mock('@radix-ui/react-context-menu', () => ({
  Separator: () => <div data-testid="separator">Separator</div>,
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ alt }: { alt: string }) => (
    <img data-testid={`image-${alt.replace(/\s+/g, '-')}`} alt={alt} />
  ),
}));

describe('HomePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the main components when data is fetched successfully', async () => {
    // Mock successful API response
    const mockServices = [
      { id: '1', name: 'Service 1', details: ['Detail 1'] },
      { id: '2', name: 'Service 2', details: ['Detail 2'] },
    ];

    (axiosInstance.get as jest.Mock).mockResolvedValue({ data: mockServices });

    // Use React's testing utilities to render the component
    const { findByTestId } = render(await HomePage());

    // Verify main components are rendered (carousel removed)
    expect(await findByTestId('new-hero-component')).toBeInTheDocument();
    expect(await findByTestId('provider-info-flex')).toBeInTheDocument();
    expect(await findByTestId('advantages')).toBeInTheDocument();
    expect(await findByTestId('how-it-works')).toBeInTheDocument();
    expect(await findByTestId('dynamic-faq')).toBeInTheDocument();
    expect(await findByTestId('service-nav-menu')).toBeInTheDocument();
    expect(await findByTestId('ask-for-service')).toBeInTheDocument();
  });

  it('renders error display when API call fails', async () => {
    // Mock failed API response
    (axiosInstance.get as jest.Mock).mockRejectedValue(new Error('API Error'));

    // Render the component with the error state
    const { findByTestId } = render(await HomePage());

    // Verify that the error message is displayed
    const errorDisplay = await findByTestId('error-display');
    expect(errorDisplay).toBeInTheDocument();
    expect(errorDisplay.textContent).toBe('Erro ao carregar serviços');
  });

  it('renders error display when API returns AxiosError', async () => {
    // Mock failed API response with AxiosError
    const axiosError = new Error('API Error');
    (axiosError as any).isAxiosError = true;
    (axiosError as any).message = 'Network Error';
    (axiosInstance.get as jest.Mock).mockRejectedValue(axiosError);

    // Render the component with the error state
    const { findByTestId } = render(await HomePage());

    // Verify that the error message is displayed
    const errorDisplay = await findByTestId('error-display');
    expect(errorDisplay).toBeInTheDocument();
    expect(errorDisplay.textContent).toBe('Erro ao carregar serviços');
  });

  it('renders main components when API returns empty categories', async () => {
    // Mock successful API response but with empty categories
    (axiosInstance.get as jest.Mock).mockResolvedValue({ data: { categories: [] } });

    // Render the component
    const { findByTestId } = render(await HomePage());

    // Verify that the main components are still rendered with empty data
    expect(await findByTestId('new-hero-component')).toBeInTheDocument();
    expect(await findByTestId('provider-info-flex')).toBeInTheDocument();
  });
});
