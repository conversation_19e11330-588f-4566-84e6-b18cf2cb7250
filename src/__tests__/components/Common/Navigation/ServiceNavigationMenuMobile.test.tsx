import { ServiceNavigationMenuMobile } from '@/src/app/_components/Common/Navigation/ServiceNavigationMenuMobile';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock the ServiceContext
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: () => ({
    services: [
      {
        id: '1',
        name: 'Assistência Técnica',
        slug: 'assistencia-tecnica',
        subcategories: [
          {
            id: '1-1',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            slug: 'celulares',
            services: [{ slug: 'conserto-celular' }],
          },
          {
            id: '1-2',
            name: 'Eletrodomésticos',
            slug: 'eletrodomesticos',
            services: [{ slug: 'conserto-geladeira' }],
          },
        ],
      },
      {
        id: '2',
        name: 'Reformas',
        slug: 'reformas',
        subcategories: [
          {
            id: '2-1',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            slug: 'pinturas',
            services: [{ slug: 'pintura-residencial' }],
          },
        ],
      },
    ],
    isLoading: false,
    error: null,
  }),
  ServiceContext: {
    Provider: ({ children }) => children,
  },
}));

// Mock the Icon and AskForService components
jest.mock('@/src/app/_components', () => ({
  Icon: ({ name, className }: { name: string; className?: string }) => (
    <svg data-testid={`icon-${name}`} className={className} role="img" aria-label={name} />
  ),
  AskForService: ({ variant, showIcon }: { variant: string; showIcon: boolean }) => (
    <div data-testid="ask-for-service" data-variant={variant} data-show-icon={String(showIcon)}>
      Ask For Service Component
    </div>
  ),
  IconName: jest.requireActual('@/src/app/_components').IconName,
}));

// Mock categories
jest.mock('@/src/app/_data/categories', () => ({
  categories: [
    {
      id: 'assistencia-tecnica',
      name: 'Assistência Técnica',
      icon: 'Wrench',
      slug: 'assistencia-tecnica',
      subcategories: ['Eletrodomésticos', 'Celulares'],
    },
    {
      id: 'reformas',
      name: 'Reformas',
      icon: 'Hammer',
      slug: 'reformas',
      subcategories: ['Pinturas'],
    },
  ],
}));

// We're using the mocked services from the ServiceContext mock

describe('ServiceNavigationMenuMobile', () => {
  beforeEach(() => {
    // Clear dataLayer before each test
    window.dataLayer = [];
  });

  const renderComponent = (onLinkClick = jest.fn()) => {
    return render(<ServiceNavigationMenuMobile onLinkClick={onLinkClick} />);
  };

  it('renders the navigation menu correctly', () => {
    renderComponent();
    // Check that the navigation menu is rendered with proper aria-label
    expect(screen.getByRole('navigation', { name: 'Menu de serviços' })).toBeInTheDocument();
  });

  it('renders all categories and their subcategories', () => {
    renderComponent();

    // Check if all categories are rendered
    expect(screen.getByText('Assistência Técnica')).toBeInTheDocument();
    expect(screen.getByText('Reformas')).toBeInTheDocument();

    // Check subcategories
    expect(screen.getByText('Celulares')).toBeInTheDocument();
    expect(screen.getByText('Eletrodomésticos')).toBeInTheDocument();
    expect(screen.getByText('Pinturas')).toBeInTheDocument();
  });

  it('renders icons for categories', () => {
    renderComponent();

    // Check for the icons that are actually rendered based on our mock
    expect(screen.getByTestId('icon-Wrench')).toBeInTheDocument();
    expect(screen.getByTestId('icon-Hammer')).toBeInTheDocument();
  });

  it('does not render the AskForService component by default', () => {
    renderComponent();

    // AskForService component should not be present by default
    const askForServiceComponent = screen.queryByTestId('ask-for-service');
    expect(askForServiceComponent).not.toBeInTheDocument();
  });

  it('renders the AskForService component when showAskForService is true', () => {
    render(<ServiceNavigationMenuMobile showAskForService={true} />);

    const askForServiceComponent = screen.getByTestId('ask-for-service');
    expect(askForServiceComponent).toBeInTheDocument();
    expect(askForServiceComponent.getAttribute('data-variant')).toBe('custom');
    expect(askForServiceComponent.getAttribute('data-show-icon')).toBe('true');
  });

  it('calls onLinkClick when a subcategory link is clicked', () => {
    const mockOnLinkClick = jest.fn();
    renderComponent(mockOnLinkClick);

    // Find and click a subcategory link
    const celularesLink = screen.getByText('Celulares');
    fireEvent.click(celularesLink);

    expect(mockOnLinkClick).toHaveBeenCalledTimes(1);
  });

  it('pushes analytics event to dataLayer when a subcategory link is clicked', () => {
    renderComponent();

    // Find and click a subcategory link
    const celularesLink = screen.getByText('Celulares');
    fireEvent.click(celularesLink);

    // Check that dataLayer was updated with the correct event
    expect(window.dataLayer.length).toBe(1);
    expect(window.dataLayer[0].event).toBe('menu_click_celulares');
  });

  it('sorts categories alphabetically', () => {
    // We're testing the sorting logic which is already handled by the mock
    // The mock is set up to return categories in alphabetical order
    renderComponent();

    // Get all category names
    const categoryElements = screen.getAllByText(/Assistência Técnica|Reformas/);

    // Check that Assistência Técnica comes before Reformas (alphabetically)
    expect(categoryElements[0].textContent).toBe('Assistência Técnica');
    expect(categoryElements[1].textContent).toBe('Reformas');
  });

  it('sorts subcategories alphabetically', () => {
    // We're testing the sorting logic which is already handled by the mock
    // The mock is set up to return subcategories in alphabetical order
    renderComponent();

    // Get all subcategory links
    const subcategoryLinks = screen.getAllByText(/Celulares|Eletrodomésticos/);

    // Check that Celulares comes before Eletrodomésticos (alphabetically)
    expect(subcategoryLinks[0].textContent).toBe('Celulares');
    expect(subcategoryLinks[1].textContent).toBe('Eletrodomésticos');
  });

  it('handles subcategory without services correctly', () => {
    // Mock the ServiceContext with a subcategory that has no services
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: [
        {
          id: '1',
          name: 'Assistência Técnica',
          slug: 'assistencia-tecnica',
          subcategories: [
            {
              id: '1-1',
              name: 'Celulares',
              slug: 'celulares',
              services: [], // Empty services array
            },
          ],
        },
      ],
      isLoading: false,
      error: null,
    });

    renderComponent();

    // Check that the subcategory is still rendered
    expect(screen.getByText('Celulares')).toBeInTheDocument();

    // Find and click the subcategory link
    const celularesLink = screen.getByText('Celulares');
    fireEvent.click(celularesLink);

    // The link should still work, but with a different URL
    expect(window.dataLayer[0].event).toBe('menu_click_celulares');
  });

  it('handles subcategory with empty services array correctly', () => {
    // Mock the ServiceContext with a subcategory that has an empty services array
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: [
        {
          id: '1',
          name: 'Assistência Técnica',
          slug: 'assistencia-tecnica',
          subcategories: [
            {
              id: '1-1',
              name: 'Celulares',
              slug: 'celulares',
              services: [], // Empty services array
            },
          ],
        },
      ],
      isLoading: false,
      error: null,
    });

    renderComponent();

    // Check that the subcategory is still rendered
    expect(screen.getByText('Celulares')).toBeInTheDocument();

    // Find and click the subcategory link
    const celularesLink = screen.getByText('Celulares');
    fireEvent.click(celularesLink);

    // The link should still work, but with a different URL
    expect(window.dataLayer[0].event).toBe('menu_click_celulares');
  });

  it('handles category without icon correctly', () => {
    // Mock the categories module with empty categories
    jest.mock(
      '@/src/app/_data/categories',
      () => ({
        categories: [],
      }),
      { virtual: true }
    );

    // Mock the ServiceContext with a category that doesn't match any in the categories data
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: [
        {
          id: '3',
          name: 'Unknown Service',
          slug: 'unknown-service',
          subcategories: [
            {
              id: '3-1',
              name: 'Unknown Subcategory',
              slug: 'unknown-subcategory',
              services: [{ slug: 'unknown-service-item' }],
            },
          ],
        },
      ],
      isLoading: false,
      error: null,
    });

    renderComponent();

    // Check that the category is still rendered
    expect(screen.getByText('Unknown Service')).toBeInTheDocument();

    // The default CircleHelp icon should be used when no matching category is found
    expect(screen.getByTestId('icon-CircleHelp')).toBeInTheDocument();
  });

  it('handles click without onLinkClick prop', () => {
    // Mock the ServiceContext with default services
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: [
        {
          id: '1',
          name: 'Assistência Técnica',
          slug: 'assistencia-tecnica',
          subcategories: [
            {
              id: '1-1',
              name: 'Celulares',
              slug: 'celulares',
              services: [{ slug: 'conserto-celular' }],
            },
          ],
        },
      ],
      isLoading: false,
      error: null,
    });

    // Render without onLinkClick prop
    render(<ServiceNavigationMenuMobile />);

    // Find and click a subcategory link
    const celularesLink = screen.getByText('Celulares');
    fireEvent.click(celularesLink);

    // Should not throw an error
    expect(window.dataLayer[0].event).toBe('menu_click_celulares');
  });

  it('handles empty services array', () => {
    // Mock the ServiceContext with empty services array
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: [],
      isLoading: false,
      error: null,
    });

    // Render component
    render(<ServiceNavigationMenuMobile />);

    // Should render the navigation but no categories
    expect(screen.getByRole('navigation', { name: 'Menu de serviços' })).toBeInTheDocument();
    expect(screen.queryByText('Assistência Técnica')).not.toBeInTheDocument();

    // Should not render the AskForService component
    expect(screen.queryByTestId('ask-for-service')).not.toBeInTheDocument();
  });

  it('handles null services', () => {
    // Mock the ServiceContext with null services
    jest.spyOn(require('@/src/app/_context/ServiceContext'), 'useServiceContext').mockReturnValue({
      services: null,
      isLoading: false,
      error: null,
    });

    // Render component
    render(<ServiceNavigationMenuMobile />);

    // Should render the navigation but no categories
    expect(screen.getByRole('navigation', { name: 'Menu de serviços' })).toBeInTheDocument();
    expect(screen.queryByText('Assistência Técnica')).not.toBeInTheDocument();

    // Should not render the AskForService component
    expect(screen.queryByTestId('ask-for-service')).not.toBeInTheDocument();
  });
});
