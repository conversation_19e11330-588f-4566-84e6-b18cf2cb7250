import '@testing-library/jest-dom';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';

// Mock das funções e componentes
jest.mock('@/src/app/_functions/analytics/common/trackClickEvent', () => ({
  trackClickEvent: jest.fn(),
}));

jest.mock('@/src/app/_components', () => ({
  Icon: ({ name, className }: any) => <span data-testid={`icon-${name}`} className={className} />,
  Button: ({ children, variant, ...props }: any) => (
    <button {...props} data-variant={variant}>
      {children}
    </button>
  ),
}));

// Mock do ServiceNavigationMenuDesktop
jest.mock('@/src/app/_components/Common/Navigation/ServiceNavigationMenuDesktop', () => ({
  ServiceNavigationMenuDesktop: () => <div data-testid="service-navigation-menu-desktop" />,
}));

// Mock do dynamic import
jest.mock('next/dynamic', () => () => {
  const DynamicComponent = ({ children }: any) => <div>{children}</div>;
  DynamicComponent.displayName = 'MockedDynamicComponent';
  return DynamicComponent;
});

// Mock dos componentes de NavigationMenu
jest.mock('@/src/app/_components/Ui/navigationMenu', () => ({
  NavigationMenu: ({ children }: any) => <nav>{children}</nav>,
  NavigationMenuList: ({ children }: any) => <ul>{children}</ul>,
  NavigationMenuItem: ({ children, className }: any) => <li className={className}>{children}</li>,
}));

// Mock do Link do Next.js
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

// Importar o componente depois dos mocks
import DesktopNavigation from '@/src/app/_components/Common/Header/DesktopNavigation';
import { createRef } from 'react';

describe('DesktopNavigation Component', () => {
  // Resetar todos os mocks antes de cada teste
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  const mockProps = {
    submenuRef: createRef<HTMLDivElement>(),
    isSubmenuOpen: false,
    handleMouseEnter: jest.fn(),
    handleMouseLeave: jest.fn(),
    pathname: '/test-path',
  } as any; // Cast to avoid TypeScript issues since props aren't actually used

  it('renders the DesktopNavigation component correctly', () => {
    render(<DesktopNavigation {...mockProps} />);

    // Verificar se o menu de serviços está presente
    expect(screen.getByText('Serviços disponíveis')).toBeInTheDocument();
    expect(screen.getByTestId('icon-ChevronDown')).toBeInTheDocument();

    // Verificar se o botão Entrar está presente
    expect(screen.getByText('Entrar')).toBeInTheDocument();
  });

  it('shows service menu on mouse enter and hides on mouse leave', async () => {
    render(<DesktopNavigation {...mockProps} />);

    // Inicialmente o menu não deve estar visível
    expect(screen.queryByTestId('service-navigation-menu-desktop')).not.toBeInTheDocument();

    // Simular mouse enter no botão de serviços
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // Verificar se o menu ficou visível
    expect(screen.getByTestId('service-navigation-menu-desktop')).toBeInTheDocument();

    // Simular mouse leave
    fireEvent.mouseLeave(servicesButton!);

    // Avançar o timer para que o setTimeout execute
    act(() => {
      jest.advanceTimersByTime(200);
    });

    // Verificar se o menu foi fechado
    await waitFor(() => {
      expect(screen.queryByTestId('service-navigation-menu-desktop')).not.toBeInTheDocument();
    });
  });

  it('renders the Entrar button correctly', () => {
    render(<DesktopNavigation {...mockProps} />);

    // Verificar se o botão Entrar está presente e tem o link correto
    const entrarButton = screen.getByText('Entrar');
    expect(entrarButton).toBeInTheDocument();
    expect(entrarButton.closest('a')).toHaveAttribute('href', '/');
  });

  it('handles mouse enter on dropdown menu', () => {
    render(<DesktopNavigation {...mockProps} />);

    // Primeiro mostrar o menu
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // Pegar a referência ao dropdown
    const dropdown = screen.getByTestId('service-navigation-menu-desktop').parentElement;

    // Simular mouse leave no botão
    fireEvent.mouseLeave(servicesButton!);

    // Imediatamente simular mouse enter no dropdown
    fireEvent.mouseEnter(dropdown!);

    // Avançar o timer um pouco
    act(() => {
      jest.advanceTimersByTime(50);
    });

    // Menu deve continuar visível
    expect(screen.getByTestId('service-navigation-menu-desktop')).toBeInTheDocument();
  });

  it('toggles the chevron icon when menu is opened/closed', async () => {
    render(<DesktopNavigation {...mockProps} />);

    const chevronIcon = screen.getByTestId('icon-ChevronDown');

    // Inicialmente o ícone não deve ter a classe rotate-180
    expect(chevronIcon).not.toHaveClass('rotate-180');

    // Abrir o menu
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // O ícone deve ter a classe rotate-180
    expect(chevronIcon).toHaveClass('rotate-180');

    // Fechar o menu
    fireEvent.mouseLeave(servicesButton!);

    // Avançar o timer
    act(() => {
      jest.advanceTimersByTime(200);
    });

    // O ícone não deve ter a classe rotate-180
    await waitFor(() => {
      expect(chevronIcon).not.toHaveClass('rotate-180');
    });
  });
});
