import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock dos componentes antes de importar o Header
jest.mock('@/src/app/_components', () => ({
  Button: (props: any) => <button {...props} />,
  Icon: (props: any) => (
    <span {...props} data-testid={props.name ? `icon-${props.name}` : 'icon'} />
  ),
  Separator: (props: any) => <div {...props} data-testid="separator" />,
}));

// Mock dos hooks
jest.mock('@/src/app/_hooks/useMenu', () => ({
  useMenu: () => ({
    isMenuOpen: false,
    toggleMenu: jest.fn(),
  }),
}));

jest.mock('@/src/app/_hooks/useSubmenu', () => ({
  useSubmenu: () => ({
    isSubmenuOpen: false,
    handleMouseEnter: jest.fn(),
    handleMouseLeave: jest.fn(),
  }),
}));

const mockToggleMenu = jest.fn();
const mockSendEvent = jest.fn();

jest.mock('@/src/app/_hooks', () => ({
  useAnalyticsEventGeneric: () => ({
    sendEvent: mockSendEvent,
  }),
  useMenu: () => ({
    isMenuOpen: false,
    toggleMenu: mockToggleMenu,
  }),
  useSubmenu: () => ({
    isSubmenuOpen: false,
    handleMouseEnter: jest.fn(),
    handleMouseLeave: jest.fn(),
  }),
}));

// Mock do usePathname
jest.mock('next/navigation', () => ({
  usePathname: () => '/test-path',
}));

// Mock dos componentes dinâmicos
jest.mock('next/dynamic', () => () => {
  const DynamicComponent = (props: any) => (
    <div data-testid="dynamic-component">{props.children}</div>
  );
  DynamicComponent.displayName = 'MockedDynamicComponent';
  return DynamicComponent;
});

// Mock de imagem do Next.js
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ ...props }: any) => {
    // Remover priority que causa warnings no React
    return <img {...props} data-testid="next-image" />;
  },
}));

// Mock de Link do Next.js
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

// Importar o Header depois dos mocks
import Header from '@/src/app/_components/Common/Header/Header';
import { createRef } from 'react';

describe('Header Component', () => {
  const mockHeaderRef = createRef<HTMLElement>();
  const mockSubmenuRef = createRef<HTMLDivElement>();

  beforeEach(() => {
    jest.clearAllMocks();
    mockToggleMenu.mockClear();
    mockSendEvent.mockClear();
  });

  it('renders the Header component correctly', () => {
    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    // Check logo
    const logo = screen.getByTestId('next-image');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/images/test-logo.svg');
    expect(logo).toHaveAttribute('alt', 'GetNinjas Logo');

    // Check for mobile menu button
    const menuButton = screen.getByLabelText('Open menu');
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveClass('md:hidden');
  });

  it('calls toggleMenu when mobile menu button is clicked', () => {
    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    const menuButton = screen.getByLabelText('Open menu');
    fireEvent.click(menuButton);

    expect(mockToggleMenu).toHaveBeenCalledTimes(1);
  });

  it('uses default logo when logoPath is not provided', () => {
    render(<Header logoPath="" headerRef={mockHeaderRef} submenuRef={mockSubmenuRef} />);

    const logo = screen.getByTestId('next-image');
    expect(logo).toHaveAttribute('src', '/images/GetNinjas_logo.svg');
  });

  it('displays menu icon when menu is closed', () => {
    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    // Verifica se o ícone do menu está presente quando o menu está fechado
    expect(screen.getByTestId('icon-Menu')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-X')).not.toBeInTheDocument();
  });

  it('renders the header with correct classes for fixed positioning', () => {
    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    const header = screen.getByRole('banner');
    expect(header).toHaveClass('fixed');
    expect(header).toHaveClass('top-0');
    expect(header).toHaveClass('z-[60]');
    expect(header).toHaveClass('w-full');
    expect(header).toHaveClass('bg-white');
    expect(header).toHaveClass('shadow-[0px_1px_4px_0px_rgba(0,0,0,0.08)]');
  });

  it('renders the logo link with href to homepage', () => {
    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    const logoLink = screen.getByRole('link', { name: /GetNinjas Logo/i });
    expect(logoLink).toHaveAttribute('href', '/');
  });

  it('passes correct props to the DesktopNavigation component', () => {
    // Mock para capturar os props passados para o DesktopNavigation
    jest.mock('next/dynamic', () => () => {
      return (props: any) => <div data-testid="desktop-navigation-props" {...props} />;
    });

    const { _isSubmenuOpen, _handleMouseEnter, _handleMouseLeave } = jest
      .requireMock('@/src/app/_hooks/useSubmenu')
      .useSubmenu();

    render(
      <Header
        logoPath="/images/test-logo.svg"
        headerRef={mockHeaderRef}
        submenuRef={mockSubmenuRef}
      />
    );

    // Verificar se os componentes dinâmicos estão sendo renderizados
    expect(screen.getAllByTestId('dynamic-component').length).toBeGreaterThan(0);
  });
});
