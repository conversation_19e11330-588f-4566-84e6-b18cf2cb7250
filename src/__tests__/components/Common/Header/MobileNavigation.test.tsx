import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';

// Mock the components used by MobileNavigation
jest.mock('@/src/app/_components', () => ({
  ServiceNavigationMenuMobile: () => <div data-testid="service-navigation-menu-mobile" />,
}));

// Mock the CancellationInfo component
jest.mock('@/src/app/_components/Pages/Home/CancellationInfo', () => ({
  CancellationInfo: () => <div data-testid="cancellation-info" />,
}));

// Import the actual component after mocking its dependencies
import MobileNavigation from '@/src/app/_components/Common/Header/MobileNavigation';

describe('MobileNavigation Component', () => {
  const mockProps = {
    isMenuOpen: true,
    toggleMenu: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the MobileNavigation component when menu is open', () => {
    render(<MobileNavigation {...mockProps} />);

    // Check if the mobile navigation menu is present
    const mobileMenu = screen.getByTestId('service-navigation-menu-mobile');
    expect(mobileMenu).toBeInTheDocument();

    // Check if the cancellation info component is present
    const cancellationInfo = screen.getByTestId('cancellation-info');
    expect(cancellationInfo).toBeInTheDocument();
  });

  it('does not render anything when menu is closed', () => {
    render(<MobileNavigation isMenuOpen={false} toggleMenu={jest.fn()} />);

    // Check that no elements are present
    expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
    expect(screen.queryByTestId('service-navigation-menu-mobile')).not.toBeInTheDocument();
    expect(screen.queryByTestId('cancellation-info')).not.toBeInTheDocument();
  });

  it('renders with the correct props', () => {
    render(<MobileNavigation {...mockProps} />);

    // Verify that the component renders with the correct props
    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();

    // Check that ServiceNavigationMenuMobile is rendered
    expect(screen.getByTestId('service-navigation-menu-mobile')).toBeInTheDocument();
  });

  it('renders with correct z-index and positioning', () => {
    render(<MobileNavigation {...mockProps} />);

    // Check if the nav element has the correct classes
    const nav = screen.getByRole('navigation');
    expect(nav).toHaveClass('fixed');
    expect(nav).toHaveClass('inset-0');
    expect(nav).toHaveClass('z-50');
    expect(nav).toHaveClass('md:hidden');
  });

  it('has the correct style for mobile view', () => {
    render(<MobileNavigation {...mockProps} />);

    // Verify that the component has the correct style for mobile view
    const nav = screen.getByRole('navigation');
    expect(nav.style.top).toBe('116px');
  });

  it('renders the inner container with correct classes', () => {
    render(<MobileNavigation {...mockProps} />);

    // Check the inner container classes
    const innerContainer = screen.getByRole('navigation').firstChild;
    expect(innerContainer).toHaveClass('h-full');
    expect(innerContainer).toHaveClass('overflow-y-auto');
    expect(innerContainer).toHaveClass('bg-white');
  });

  it('renders the CancellationInfo component with correct border styling', () => {
    render(<MobileNavigation {...mockProps} />);

    // Find the container for CancellationInfo
    const cancellationInfoContainer = screen.getByTestId('cancellation-info').parentElement;
    expect(cancellationInfoContainer).toHaveClass('border-t');
    expect(cancellationInfoContainer).toHaveClass('border-gray-200');
  });
});
