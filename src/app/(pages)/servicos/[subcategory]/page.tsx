import { ClientRedirectFallback, ClientServicePage } from '@/src/app/_components';
import { serviceTypeApi } from '@/src/app/_services/serviceTypeApi';
import { generateDynamicMetadata } from '@/src/app/_utils/dynamicMetadata';
import { Metadata } from 'next';
import { Suspense } from 'react';

type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export async function generateMetadata(props: {
  params: Promise<{ subcategory: string }>;
  searchParams: SearchParams;
}): Promise<Metadata> {
  const params = await props.params;
  const { subcategory } = params;

  // Get the search params
  const searchParamsObj = await props.searchParams;

  // Look specifically for the empty key parameter
  const emptyKeyParam = searchParamsObj[''] || '';
  const rawSlug = emptyKeyParam || Object.values(searchParamsObj)[0];
  const slug = Array.isArray(rawSlug) ? rawSlug[0] : rawSlug || '';

  // Use our enhanced metadata generator
  return generateDynamicMetadata(slug, subcategory);
}

/**
 * Generate static params for all subcategories
 * This helps Next.js pre-render these routes at build time
 */
export async function generateStaticParams() {
  return serviceTypeApi.getSubcategories();
}

// Enable Incremental Static Regeneration with a revalidation period of 1 hour
export const revalidate = 3600;

export default async function ServicePage(props: {
  params: Promise<{ subcategory: string }>;
  searchParams: SearchParams;
}) {
  // We don't need the subcategory param in this component, but we await it for consistency
  await props.params;

  // Get the search params
  const searchParamsObj = await props.searchParams;

  // Look specifically for the empty key parameter
  const emptyKeyParam = searchParamsObj[''] || '';
  const rawSlug = emptyKeyParam || Object.values(searchParamsObj)[0];
  const slug = Array.isArray(rawSlug) ? rawSlug[0] : rawSlug || '';

  if (!slug) {
    return (
      <Suspense fallback={null}>
        <ClientRedirectFallback />
      </Suspense>
    );
  }

  // Pass the slug to the client component, which will fetch the service data
  return <ClientServicePage slug={slug} />;
}
