import {
  Advantages,
  AskForService,
  DynamicDelayedSurveyMonkey,
  DynamicFAQ,
  ErrorDisplay,
  HowItWorks,
  NewHero,
  ServiceNavigationMenuDesktop,
  ServiceNavigationMenuMobile,
} from '@/src/app/_components';

import Image from 'next/image';

import { axiosInstance } from '@/src/app/_utils/';
import { Separator } from '@radix-ui/react-context-menu';
import { AxiosError } from 'axios';

// Normal server component code continues here
async function getServices() {
  try {
    const response = await axiosInstance.get('/service-type/list', {
      headers: {
        'service-provider': 'EUR',
      },
    });

    const data = await response.data;
    return data.categories || []; // Retorna a lista de categorias
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`API error: ${error.message}`);
    }
    throw error;
  }
}

export default async function HomePage() {
  let services;

  try {
    services = await getServices();
  } catch (error) {
    console.error('Failed to fetch services:', error);
    return <ErrorDisplay fullPage message="Erro ao carregar serviços" />;
  }

  return (
    <>
      <DynamicDelayedSurveyMonkey />

      {/* Hero Section */}
      <div
        className="relative w-full bg-gradient-to-br"
        style={{
          background: 'linear-gradient(195deg,rgb(217, 221, 226) -21.73%, #FFF 41.77%)',
        }}
      >
        <section className="mx-auto max-w-fit px-6 py-8 lg:p-12">
          <NewHero services={services} />
        </section>
      </div>

      {/* Menu de Navegação de Serviços */}
      <section className="relative z-40 mx-auto mb-6 mt-12 max-w-7xl border-t-gray-200 px-8 lg:px-12">
        <h2 className="my-8 text-3xl font-extrabold">Disponível para agendar agora</h2>

        {/* Desktop Navigation Menu */}
        <div className="hidden py-10 md:block 2xl:-ml-40">
          <ServiceNavigationMenuDesktop
            className="w-full"
            categoryIconClassName="h-5 w-5 text-gray-500 stroke-2"
            containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
            categoryClassName="mb-6"
            categoryTitleClassName="flex items-center gap-2 mb-2"
            subcategoryListClassName="ml-7 space-y-3"
            subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
          />
        </div>

        {/* Mobile Navigation Menu */}
        <div className="block md:hidden">
          <ServiceNavigationMenuMobile />
        </div>
      </section>

      {/* Como funciona */}
      <section className="relative z-10 mx-auto mb-20 max-w-7xl px-8 py-12 md:py-8 lg:p-12">
        <HowItWorks />
      </section>

      {/* SVG Wave */}
      <div className="relative z-0 mb-[-58%] mt-16 w-full md:mb-[-50%]">
        <Image className="z-0" width={3000} height={100} alt="svg wave" src="/images/wave.svg" />
      </div>

      {/* Vantagens */}
      <section className="relative z-20 mx-auto max-w-7xl px-8 py-12 md:py-20 lg:px-12">
        <Advantages />
      </section>

      {/* FAQ Section */}
      <section className="relative z-30 mx-auto max-w-7xl px-8 py-20 lg:mt-20 lg:p-12">
        <DynamicFAQ />
      </section>

      <Separator className="relative z-40 h-[1px] w-full bg-gray-300 lg:mt-14" />

      {/* Menu de Navegação de Serviços */}
      <section className="relative z-40 mx-auto mb-16 mt-12 max-w-7xl border-t-gray-200 px-8 lg:px-12">
        <h2 className="mb-4 mt-8 text-3xl font-extrabold">O que você precisa?</h2>

        {/* Desktop Navigation Menu */}
        <div className="hidden py-10 md:block 2xl:-ml-40">
          <ServiceNavigationMenuDesktop
            className="w-full"
            categoryIconClassName="h-5 w-5 text-gray-500 stroke-2"
            containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
            categoryClassName="mb-6"
            categoryTitleClassName="flex items-center gap-2 mb-2"
            subcategoryListClassName="ml-7 space-y-3"
            subcategoryLinkClassName="text-sm font-medium hover:scale-105 transition-transform text-muted-foreground hover:text-gray-900"
          />
        </div>

        {/* Mobile Navigation Menu */}
        <div className="block md:hidden">
          <ServiceNavigationMenuMobile />
        </div>
      </section>

      {/* Seção de pedir serviço personalizado */}
      <section className="relative z-40 mx-auto mb-16 max-w-7xl px-8 lg:px-12">
        <AskForService
          variant="custom"
          className="relative w-full overflow-hidden lg:mx-auto"
          containerClassName="relative z-10 flex flex-col items-start justify-between gap-10 lg:flex-row lg:items-center"
          titleClassName="text-2xl font-bold leading-tight sm:text-3xl"
          descriptionClassName=" max-w-2xl text-base sm:text-lg"
          buttonClassName="w-full rounded-xl bg-white px-6 py-4 text-base font-bold text-black transition duration-300 hover:bg-gray-100 sm:px-8 sm:py-6 sm:text-lg"
          showIcon={true}
        />
      </section>
    </>
  );
}
