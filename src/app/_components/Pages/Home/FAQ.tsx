'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/src/app/_components/Ui/accordion';
import { useAnalyticsEventGeneric } from '@/src/app/_hooks';

export function FAQ() {
  const { sendEvent } = useAnalyticsEventGeneric();
  const faqs = [
    {
      question: 'Como funciona o serviço GetNinjas + Europ Assistance?',
      answer:
        'O GetNinjas + Europ Assistance é uma parceria que oferece serviços residenciais com preço fechado, profissionais qualificados e garantia de qualidade. Você escolhe o serviço, agenda a data e recebe o profissional em sua casa.',
    },
    {
      question: 'Por que devo confiar nesse modelo?',
      answer:
        'O GetNinjas tem mais de 13 anos de experiência conectando clientes a profissionais qualificados, e a Europ Assistance possui mais de 60 anos de experiência em assistência residencial. Juntos, garantimos um serviço de qualidade com profissionais verificados e garantia de 90 dias.',
    },
    {
      question: 'Quais os diferenciais desse serviço?',
      answer:
        'Preço fechado: sem surpresas, você sabe exatamente quanto vai pagar. Caso sejam necessárias peças extras ou reparos adicionais, o profissional informará o custo antes de começar a execução. Agendamento simples e rápido: sem a necessidade de múltiplos orçamentos. Profissionais qualificados: atendimento garantido pela Europ Assistance. Segurança e garantia: suporte caso algo não saia como esperado.',
    },
    {
      question: 'Como faço para agendar um serviço GetNinjas + Europ Assistance?',
      answer:
        'É simples! Escolha o serviço desejado no site, selecione a data e horário disponíveis que melhor se encaixam na sua agenda, informe seu endereço e finalize o agendamento. Você receberá uma confirmação por e-mail e SMS.',
    },
    {
      question: 'O serviço tem garantia?',
      answer:
        'Sim, todos os serviços realizados têm garantia de 90 dias. Se houver qualquer problema com o serviço realizado dentro desse período, entre em contato conosco que enviaremos um profissional para resolver.',
    },
    {
      question: 'Quanto tempo leva para o profissional chegar?',
      answer:
        'Você pode agendar o serviço para o mesmo dia ou para datas futuras, dependendo da disponibilidade. No dia agendado, o profissional entrará em contato para confirmar o horário exato da visita.',
    },
    {
      question: 'Os valores exibidos incluem todos os custos do serviço?',
      answer:
        'Sim, o valor exibido é o preço final do serviço básico. Caso sejam necessários materiais adicionais ou serviços extras, o profissional informará o custo adicional antes de realizar o trabalho, e você poderá decidir se deseja prosseguir.',
    },
    {
      question: 'Posso cancelar ou reagendar o serviço?',
      answer:
        'Sim, você pode cancelar ou reagendar seu serviço até 24 horas antes do horário agendado sem custo adicional. Cancelamentos com menos de 24 horas de antecedência podem estar sujeitos a uma taxa.',
    },
    {
      question: 'Não encontrei o serviço que eu preciso. O que fazer?',
      answer:
        'Se você não encontrou o serviço específico que precisa, recomendamos que visite o site do GetNinjas (getninjas.com.br) onde você encontrará uma variedade maior de categorias e poderá solicitar orçamentos personalizados de profissionais.',
    },
  ];

  return (
    <div className="relative z-30">
      <div className="flex flex-col justify-between gap-12 md:flex-row">
        <div className="place-content-start md:w-1/4">
          <h2 className="text-3xl font-bold text-slate-900">
            Dúvidas <br /> frequentes
          </h2>
        </div>
        <div className="md:w-3/4">
          <Accordion
            type="single"
            collapsible
            defaultValue="faq-0"
            className="w-full space-y-1 rounded-2xl bg-muted px-8"
          >
            {faqs.map((faq, index) => (
              <AccordionItem
                className="overflow-hidden border-b border-slate-200 px-3 py-0 last:mb-0 md:px-6"
                key={index}
                value={`faq-${index}`}
              >
                <AccordionTrigger
                  onClick={() =>
                    sendEvent(`click_faq_question_${index + 1}`, { question: faq.question })
                  }
                  className="py-5 text-left text-base font-semibold text-slate-900 hover:no-underline"
                >
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="pb-5 pt-0 text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
