import { ServiceCardDeck } from '@/src/app/_components/Pages/Home/ServiceCardDeck';
import { ServiceCategory } from '@/src/app/_interfaces/service-type';

interface NewHeroProps {
  services?: ServiceCategory[];
}

export const Hero = ({ services = [] }: NewHeroProps) => {
  // Flatten services from categories for the card deck
  const allServices = services.flatMap(
    (category) => category.subcategories?.flatMap((subcategory) => subcategory.services || []) || []
  );

  return (
    <div className="flex w-full flex-col items-center justify-center text-black lg:flex-row lg:justify-between lg:gap-10">
      {/* Service Card Deck - Mobile: Above title, Desktop: Right side */}
      <div className="order-1 mr-24 mt-0 flex w-5/12 items-center justify-center md:mr-0 lg:order-2 lg:mr-0 lg:mt-8 lg:justify-start">
        <ServiceCardDeck
          services={allServices}
          className="flex-shrink-0 scale-[80%] lg:scale-100"
        />
      </div>

      {/* Hero Content - Mobile: Below card deck, Desktop: Left side */}
      <div className="order-2 w-full md:w-7/12 lg:order-1">
        <h1 className="text-5xl font-extrabold lg:text-6xl">
          <strong className="bg-gradient-to-r from-amber-500 via-yellow-400 to-yellow-300 bg-clip-text text-transparent">
            Serviço <br /> garantido, <br />
          </strong>
          seguro e sem complicação.
        </h1>
        <p className="mt-6 text-xl font-medium lg:mt-4 lg:text-xl">
          O GetNinjas ajuda você a contratar o serviço que você precisa com <br /> segurança e
          agilidade, em parceria com a Europ Assistance.
        </p>
      </div>
    </div>
  );
};

// Export as NewHero for backward compatibility
export const NewHero = Hero;
