'use client';

import { AskForService, Icon, IconName } from '@/src/app/_components';
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { categories } from '@/src/app/_data/categories';
import Link from 'next/link';

interface ServiceNavigationMenuMobileProps {
  onLinkClick?: () => void;
}

export function ServiceNavigationMenuMobile({ onLinkClick }: ServiceNavigationMenuMobileProps) {
  const { services } = useServiceContext();

  // Sort categories alphabetically by name
  const sortedServices =
    services && services.length > 0
      ? [...services].sort((a, b) => a.name.localeCompare(b.name))
      : [];

  return (
    <nav className="w-full p-8" aria-label="Menu de serviços">
      {/* Services menu - Following Figma design structure */}
      <div className="flex flex-col gap-8">
        {sortedServices.map((category, index) => {
          const matched = categories.find((c) => c.id === category.slug);
          const iconName = matched ? matched.icon : 'CircleHelp';

          // Sort subcategories alphabetically by name
          const sortedSubcategories = [...(category.subcategories || [])].sort((a, b) =>
            a.name.localeCompare(b.name)
          );

          return (
            <div key={category.id}>
              <div className="flex gap-10">
                {/* Category column - Fixed width 128px as per Figma */}
                <div className="flex w-32 items-start gap-2">
                  <div className="flex h-5 w-5 items-center justify-center">
                    <Icon name={iconName as IconName} className="h-5 w-5 stroke-2 text-[#62748E]" />
                  </div>
                  <span className="text-base font-semibold leading-6 text-[#020618]">
                    {category.name}
                  </span>
                </div>

                {/* Subcategories column */}
                <div className="flex-1">
                  <ul className="space-y-2">
                    {sortedSubcategories.map((subcategory) => {
                      // Create the URL for the first service in the subcategory
                      const href =
                        subcategory.services && subcategory.services.length > 0
                          ? `/servicos/${subcategory.slug}?=${subcategory.services[0]?.slug}`
                          : `/servicos/${subcategory.slug}`;

                      return (
                        <li key={`${category.id}-${subcategory.id}`}>
                          <Link
                            href={href}
                            className="flex h-12 w-full items-center text-left text-base font-medium leading-6 text-[#62748E] transition-colors hover:text-gray-900"
                            title={`Ver serviços de ${subcategory.name}`}
                            onClick={() => {
                              window.dataLayer = window.dataLayer || [];
                              window.tagManagerDataLayer =
                                window.tagManagerDataLayer || window.dataLayer;

                              const eventData = {
                                event: `menu_click_${subcategory.name.replace(/\s+/g, '_').toLowerCase()}`,
                              };

                              // Push to dataLayer
                              window.dataLayer.push(eventData);

                              // Push to tagManagerDataLayer if it's different from dataLayer
                              if (window.tagManagerDataLayer !== window.dataLayer) {
                                window.tagManagerDataLayer.push(eventData);
                              }

                              if (onLinkClick) onLinkClick();
                            }}
                          >
                            <span className="inline-block">{subcategory.name}</span>
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>

              {/* Divider line between categories - Following Figma design */}
              {index < sortedServices.length - 1 && (
                <div className="mt-8 h-px w-full bg-[#E2E8F0]" />
              )}
            </div>
          );
        })}
      </div>

      {/* Ask for service section */}
      <section className="mt-10 pt-2">
        <AskForService variant="custom" showIcon />
      </section>
    </nav>
  );
}
