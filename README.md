# 🛒 E-commerce Frontend

Este repositório contém a interface frontend do e-commerce da GetNinjas em parceria com a Europ Assistance. Desenvolvido com **Next.js** e estilizado com **Tailwind CSS**, este projeto visa oferecer uma experiência rápida e intuitiva para contratação de serviços..

## 📥 Instalação

Clone o repositório e instale as dependências utilizando **pnpm**:

```sh
# Clone o repositório
git clone https://github.com/getninjas/ecommerce-frontend.git

# Acesse o diretório do projeto
cd ecommerce-frontend

# Instale o pnpm caso não tenha
npm install -g pnpm

# Instale as dependências do projeto
pnpm install
```

## 🛠 Requisitos

Para executar o projeto localmente, você precisará de:

- **Node.js** (versão mais atualizada recomendada)
- **pnpm** (gerenciador de pacotes).

Para rodar o projeto:

```sh
pnpm dev
```

O servidor iniciará em `http://localhost:3000` por padrão.

---

## 🌳 Fluxo de Desenvolvimento

### Estrutura de Branches

- `main` → Branch de produção
- `developer` → Branch de desenvolvimento (sempre sincronizada com main)
- `release/*` → Branches para desenvolvimento de novas features
- `hotfix/*` → Branches para correções urgentes em produção

### Fluxo Normal de Desenvolvimento

1. **Iniciando Nova Feature**:

```sh
git checkout developer
git pull origin developer
git checkout -b release/v1.x.x
```

2. **Desenvolvimento de Features**:

```sh
# Criar branch de feature
git checkout release/v1.x.x
git checkout -b feature/nova-funcionalidade

# Após desenvolvimento, mergear na release
git checkout release/v1.x.x
git merge feature/nova-funcionalidade
```

3. **Processo de Release**:

```sh
# Quando release estiver pronta
git checkout developer
git merge release/v1.x.x
git push origin developer

git checkout main
git merge developer
git push origin main

# Criar tag da release
git tag -a v1.x.x -m "Release v1.x.x"
git push origin v1.x.x
```

### Fluxo de Hotfix

1. **Criando Hotfix**:

```sh
git checkout main
git pull origin main
git checkout -b hotfix/fix-bug-critico
```

2. **Aplicando Hotfix**:

```sh
# Após implementar e testar o fix
git checkout main
git merge hotfix/fix-bug-critico
git push origin main

# Criar tag do hotfix
git tag -a v1.x.x-hotfix.1 -m "Hotfix: correção crítica"
git push origin v1.x.x-hotfix.1

# Sincronizar com developer
git checkout developer
git merge main
git push origin developer
```

### Ambientes

- **Desenvolvimento**: Branches `release/*`
- **Homologação**: Branch `developer`
- **Produção**: Branch `main`

### Regras de Proteção

- Não commitar diretamente em `main` ou `developer`
- Manter `main` e `developer` sempre sincronizados
- Desenvolvimento acontece em branches `release/*`
- Hotfixes seguem fluxo especial para correções urgentes

---

## 🚀 Funcionalidades

### 🌍 Página principal

Atualmente configurada para redirecionar automaticamente para:

```text
/service/conserto-de-chuveiro-europ-assistance
```

Esse é o serviço principal da parceria Europ Assistance x GetNinjas.

### 🔍 `/service/:slug`

Esta é a página principal da aplicação, onde ocorre a dinamicidade do projeto.

- **Slug dinâmico**: A página recebe o `service-type` do Backend e renderiza as informações correspondentes ao serviço.
- **Parâmetros da URL**: O `slug` é extraído da URL para carregar as informações do serviço correto.

### 📝 `/checkout`

A página onde ocorre a finalização do pedido:

- Formulário de contratação de serviço.
- **Validação com Zod**.
- Requisição ao Backend para **agendamento e pagamento**.
- Redirecionamento para o **serviço de pagamento externo (Iugu)**.

### 🎉 `/success`

Página de sucesso, acessada após a conclusão do pagamento:

- A URL contém um `UID` (`/success&21khj4h21jhk2g1k4g`).
- O `UID` é extraído e utilizado para uma requisição GET ao Backend.
- A resposta do Backend contém os detalhes do pedido e agendamento.

---

## 📦 Principais Bibliotecas

- **[Zod](https://zod.dev/)** → Validação de formulários
- **[React Day Picker](https://react-day-picker.js.org/)** → Seleção de datas
- **[Lucide React](https://lucide.dev/)** → Ícones modernos
- **[Radix UI](https://www.radix-ui.com/)** → Componentes acessíveis

---

## 📌 Uso

Para rodar o projeto localmente:

```sh
pnpm dev
```

Isso iniciará o servidor de desenvolvimento, permitindo acesso via navegador em:

```text
http://localhost:3000
```

---

## 🧪 Testes

Este projeto utiliza Jest para testes unitários e Playwright para testes end-to-end:

### Testes Unitários com Jest

```sh
# Executar todos os testes
pnpm test

# Executar testes em modo watch
pnpm test:watch

# Verificar cobertura de testes
pnpm test:coverage
```

### Testes End-to-End com Playwright

```sh
# Executar todos os testes E2E
pnpm test:e2e

# Executar testes E2E em navegadores específicos
pnpm test:e2e:chromium
pnpm test:e2e:firefox

# Executar testes E2E em navegadores móveis
pnpm test:e2e:mobile-chrome

# Executar testes com interface visual
pnpm test:e2e:ui

# Executar testes em modo debug
pnpm test:e2e:debug

# Ver relatório de testes
pnpm test:e2e:report
```

#### Executando Testes em Diferentes Sistemas Operacionais

**macOS**:
No macOS, todos os navegadores (incluindo WebKit/Safari) funcionam sem configuração adicional:

```sh
# Executar todos os testes E2E (incluindo WebKit/Safari)
pnpm test:e2e

# Executar testes específicos para WebKit/Safari
pnpm test:e2e:webkit
pnpm test:e2e:mobile-safari
```

**Linux**:
No Linux, os testes para WebKit/Safari requerem dependências adicionais:

```sh
# Instalar dependências do WebKit
pnpm test:e2e:install-deps

# Executar testes E2E no WebKit (após instalar dependências)
pnpm test:e2e:webkit

# Executar testes E2E no Mobile Safari (após instalar dependências)
pnpm test:e2e:mobile-safari
```

**Windows**:
No Windows, os testes para WebKit/Safari também requerem dependências adicionais:

```sh
# Instalar dependências do WebKit
pnpm test:e2e:install-deps

# Executar testes E2E no WebKit (após instalar dependências)
pnpm test:e2e:webkit

# Executar testes E2E no Mobile Safari (após instalar dependências)
pnpm test:e2e:mobile-safari
```

**Nota:** Se você encontrar erros relacionados a dependências ausentes no WebKit, consulte a [documentação do Playwright sobre dependências do sistema](https://playwright.dev/docs/browsers#install-system-dependencies) para instruções específicas para seu sistema operacional.

## 📏 Linting e Formatação

Este projeto utiliza ESLint e Prettier para garantir a qualidade e consistência do código:

### ESLint

O ESLint está configurado com regras para TypeScript, React, e acessibilidade:

```sh
# Executar verificação de lint
pnpm lint

# Corrigir automaticamente problemas de lint
pnpm lint:fix
```

### Prettier

O Prettier garante a formatação consistente do código:

```sh
# Formatar todos os arquivos
pnpm format

# Verificar se os arquivos estão formatados corretamente
pnpm format:check
```

### Verificar tudo

Execute ambos lint e formatação:

```sh
# Verificar lint e formatação
pnpm check
```

### Integração com VS Code

Para obter o melhor ambiente de desenvolvimento:

1. Instale as extensões [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) e [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) no VS Code
2. A configuração do workspace já está definida no diretório `.vscode/settings.json`
3. O código será formatado automaticamente ao salvar

---

## 🖼️ Otimização de Imagens

Este projeto inclui scripts para otimização de imagens que podem ser executados localmente antes do deploy:

### Scripts de Otimização

```sh
# Otimizar todas as imagens em public/images (versão Sharp - recomendada)
pnpm optimize-images-sharp

# Definir diretório de origem personalizado
pnpm optimize-images-sharp -- --sourceDir=public/uploads

# Definir nível de qualidade personalizado (1-100)
pnpm optimize-images-sharp -- --quality=85

# Apenas criar versões WebP sem modificar os originais
pnpm optimize-images-sharp -- --webp-only

# Versão alternativa (usando imagemin)
pnpm optimize-images
```

### Otimização Avançada de WebP Grandes

Para otimizar ainda mais imagens WebP grandes (acima de 1MB):

```sh
# Otimizar WebP grandes com qualidade 50 e redimensionar para 1200x800
pnpm optimize-large-webp -- --quality=50 --resize=1200x800

# Definir limite de tamanho personalizado (em KB)
pnpm optimize-large-webp -- --sizeThreshold=500

# Simular a otimização sem modificar os arquivos (dry run)
pnpm optimize-large-webp -- --dryRun
```

Este script é especialmente útil para reduzir drasticamente o tamanho de imagens WebP grandes, mantendo uma qualidade aceitável para a web.

### Ferramenta Avançada de Imagens

Uma ferramenta mais avançada com recursos adicionais:

```sh
# Ver ajuda e opções disponíveis
pnpm image-tools help

# Otimizar imagens
pnpm image-tools optimize --quality=85

# Converter imagens para WebP
pnpm image-tools convert --sourceDir=public/uploads

# Redimensionar imagens
pnpm image-tools resize --width=800 --height=600

# Gerar conjunto de imagens responsivas
pnpm image-tools responsive --sizes=320,640,1024,1920
```

### Otimização Completa de Todas as Imagens

Para otimizar todas as imagens de uma só vez (conversão para WebP, otimização avançada e remoção de originais):

```sh
pnpm optimize-all
```

Este comando executa todos os passos de otimização em sequência, resultando em imagens altamente otimizadas para a web.

### Otimização Automática no Build

As imagens são automaticamente otimizadas antes do build quando você executa localmente:

```sh
pnpm build
```

Isso garante que todas as imagens estejam otimizadas antes do deploy, melhorando o desempenho do site.

### Pular Otimização de Imagens

A otimização de imagens é automaticamente pulada em ambientes Docker/CI para evitar problemas de dependências. Se você quiser pular a otimização de imagens localmente, você pode definir a variável de ambiente `SKIP_IMAGE_OPTIMIZATION`:

```sh
SKIP_IMAGE_OPTIMIZATION=true pnpm build
```

**Importante:** Sempre otimize as imagens localmente antes de fazer deploy, pois a otimização não será executada durante o build em ambientes Docker/CI.

### Remover Arquivos Originais

Após a otimização e conversão para WebP, você pode remover os arquivos originais para economizar espaço:

```sh
# Remover arquivos originais em um diretório específico
pnpm delete-originals -- --sourceDir=public/images/pasta/especifica

# Simular a remoção sem excluir os arquivos (dry run)
pnpm delete-originals -- --dryRun
```

Esta ação é irreversível, então use com cuidado e sempre faça um backup ou use a opção `--dryRun` primeiro.

---

Feito com ❤️ pela equipe GetNinjas 🚀
